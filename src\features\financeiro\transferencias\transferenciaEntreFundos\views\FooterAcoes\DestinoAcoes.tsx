import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const FundoDestinoAcoes = (): React.ReactElement => {
  const {
    setVerMais,
    verMais,
    distribuicaoValores,
    setModalProsseguir,
    setErro,
  } = TransferenciaEntreFundos.useTransferenciaContext();

  return (
    <>
      <TransferenciaEntreFundos.Grid
        justify="space-between"
        alignitem="center"
        style={{ height: 'auto' }}
      >
        <TransferenciaEntreFundos.GridItem>
          <TransferenciaEntreFundos.Text variant="text-standard-400">
            {TransferenciaEntreFundos.CONSTANTES.SESSOES.FOOTER_RESTANTE}{' '}
            <strong>
              {TransferenciaEntreFundos.tryGetMonetaryValueOrDefault(
                distribuicaoValores.distribuicaoRestante,
                '0',
              )}
            </strong>
          </TransferenciaEntreFundos.Text>
        </TransferenciaEntreFundos.GridItem>
        <TransferenciaEntreFundos.Grid
          style={{ flexDirection: 'column' }}
          margin="0"
        >
          <TransferenciaEntreFundos.GridItem>
            <TransferenciaEntreFundos.Text variant="text-standard-400">
              {TransferenciaEntreFundos.CONSTANTES.SESSOES.FOOTER_TOTAL}{' '}
              <strong>
                {TransferenciaEntreFundos.tryGetMonetaryValueOrDefault(
                  distribuicaoValores.somaEntreFundosDestino,
                  '0',
                )}
              </strong>
            </TransferenciaEntreFundos.Text>

            <TransferenciaEntreFundos.DestinoAcoesButton
              onClick={() => setVerMais(!verMais)}
              variant="auxiliary"
              leftIcon={
                <TransferenciaEntreFundos.AddCircleOutline height="16px" />
              }
              size="small"
            >
              <TransferenciaEntreFundos.Text variant="text-standard-600">
                {TransferenciaEntreFundos.getTernaryResult(
                  verMais,
                  TransferenciaEntreFundos.CONSTANTES.SESSOES.FOOTER_VER_MENOS,
                  TransferenciaEntreFundos.CONSTANTES.SESSOES.FOOTER_VER_MAIS,
                )}
              </TransferenciaEntreFundos.Text>
            </TransferenciaEntreFundos.DestinoAcoesButton>
          </TransferenciaEntreFundos.GridItem>
        </TransferenciaEntreFundos.Grid>
      </TransferenciaEntreFundos.Grid>

      <TransferenciaEntreFundos.Grid justify="flex-end" margin="3rem 0 0">
        <TransferenciaEntreFundos.Button
          variant="secondary"
          onClick={() => {
            setModalProsseguir(true);
            setErro();
          }}
        >
          {TransferenciaEntreFundos.CONSTANTES.SESSOES.FOOTER_BOTAO_PROSSEGUIR}
        </TransferenciaEntreFundos.Button>
      </TransferenciaEntreFundos.Grid>
    </>
  );
};
