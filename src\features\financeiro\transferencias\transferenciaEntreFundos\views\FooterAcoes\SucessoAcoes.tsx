import * as Transferencia from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const TransferenciaEntreFundosSucessoAcoes =
  (): React.ReactElement | null => {
    const { transferenciaRealizada, setErro } =
      Transferencia.useTransferenciaContext();
    const navigate = Transferencia.useNavigate();
    const { handleObterComprovante, loadingComprovantes, error, idRequisicao } =
      Transferencia.useComprovanteTransferencia();
    const theme = Transferencia.useTheme();

    if (!transferenciaRealizada) return null;

    if (error) {
      setErro(Transferencia.EErroTransferencia.ComprovanteTransferencia);
      return null;
    }

    return (
      <Transferencia.Grid
        gap="1rem"
        style={{ padding: '2rem' }}
        justify="flex-end"
      >
        <Transferencia.Button
          variant="secondary"
          onClick={() => {
            handleObterComprovante();
          }}
        >
          <Transferencia.SwitchCase>
            <Transferencia.Match
              when={loadingComprovantes?.includes(idRequisicao)}
            >
              <Transferencia.LoadingSpinner
                size="small"
                color={theme.color.palette.grayscale['0']}
              />
            </Transferencia.Match>
            <Transferencia.Match
              when={!loadingComprovantes?.includes(idRequisicao)}
            >
              {Transferencia.CONSTANTES.BOTOES_SUCESSO.COMPROVANTE}
            </Transferencia.Match>
          </Transferencia.SwitchCase>
        </Transferencia.Button>

        <Transferencia.Button
          variant="primary"
          onClick={() => {
            navigate(Transferencia.ROUTES.INICIO);
          }}
        >
          {Transferencia.CONSTANTES.BOTOES_SUCESSO.FINALIZAR}
        </Transferencia.Button>
      </Transferencia.Grid>
    );
  };
