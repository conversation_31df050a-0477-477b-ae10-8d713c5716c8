import {
  Match,
  SwitchCase,
  useTransferenciaServicosContext,
  React,
} from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';
import * as Fundos from '.';

export const TransferenciaEntreFundosLayout: React.FC = () => {
  const { consultaOrigem } = useTransferenciaServicosContext();

  return (
    <>
      <SwitchCase fallback={undefined}>
        <Match when={!consultaOrigem.loading}>
          <Fundos.AlertasTransferencia />
        </Match>
      </SwitchCase>
      <Fundos.Origem />
      <Fundos.Destino />
      <Fundos.Footer />
    </>
  );
};
