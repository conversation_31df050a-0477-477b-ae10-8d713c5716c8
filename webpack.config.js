const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react-ts");
const path = require("path");
const webpack = require("webpack");
const dotenv = require("dotenv");
const ReactRefreshWebpackPlugin = require("@pmmmwh/react-refresh-webpack-plugin");

module.exports = (webpackConfigEnv, argv) => {
  let environment = webpackConfigEnv.production
  ? 'production'
  : webpackConfigEnv.homologation
  ? 'homologation'
  : webpackConfigEnv['pre-production']
  ? 'pre-production'
  : 'development';

  const isDevelopment = environment === 'development';


  const envFile = `.env.${environment}`;
  dotenv.config({ path: envFile });

  const defaultConfig = singleSpaDefaults({
    orgName: "CVP",
    projectName: process.env.REACT_APP_NOME_MFE,
    webpackConfigEnv,
    argv,
  });

  const config = {
    CLIENT_ID: process.env.REACT_APP_CLIENT_ID,
    NOME_ACESSO_STORE_KEY: process.env.REACT_APP_NOME_ACESSO_STORE_KEY,
    BEARER_TOKEN_STORE_KEY: process.env.REACT_APP_BEARER_TOKEN_STORE_KEY,
    USER_METADATA_STOREY_KEY: process.env.REACT_APP_USER_METADATA_STOREY_KEY,
    CACHE_DURATION: process.env.REACT_APP_CACHE_DURATION,
    API_BASE_URL: process.env.REACT_APP_API_BASE_URL,
    MFE_ENV: process.env.REACT_APP_PE_ENV,
    CHAT_BASE_URL: process.env.REACT_APP_CHAT_BASE_URL,
    WEBCHAT_BASE_URL: process.env.REACT_APP_WEBCHAT_BASE_URL,
    REACT_APP_MFE_ASSINATURA_NAME: process.env.REACT_APP_MFE_ASSINATURA_NAME,
    REACT_APP_INSIGHTS_CONNECTION_STRING: process.env.REACT_APP_INSIGHTS_CONNECTION_STRING,
    REACT_APP_NOME_MFE: process.env.REACT_APP_NOME_MFE,
  };

  return merge(defaultConfig, {
    module: {
      rules: [

      ],
    },
    resolve: {
      alias: {
        '@src': path.resolve(__dirname, 'src'),
      },
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    },
    plugins: [
      new webpack.DefinePlugin({
        'AppConfig': JSON.stringify(config),
      }),
      // Adiciona React Refresh apenas em desenvolvimento
      ...(isDevelopment ? [new ReactRefreshWebpackPlugin()] : []),
    ],
    externals: {},
    ignoreWarnings: [{
      module: /@cvp\/componentes-posvenda/,
      message: /Critical dependency/,
    }],
  });
};
