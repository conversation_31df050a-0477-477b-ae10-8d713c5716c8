import * as Transferencia from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const AlertasTransferenciaCases: React.FC<
  Transferencia.TAlertasTransferenciaCases
> = ({ erro, distribuicaoValores, transferenciaRealizada }) => {
  const { consultaOrigem, mensagemErro } =
    Transferencia.useTransferenciaServicosContext();

  return (
    <Transferencia.SwitchCase fallback={undefined}>
      <Transferencia.Match
        when={erro === Transferencia.EErroTransferencia.FluxoInvalido}
      >
        <Transferencia.Alerta tipo="erro">{mensagemErro}</Transferencia.Alerta>
      </Transferencia.Match>

      <Transferencia.Match
        when={erro === Transferencia.EErroTransferencia.SemFundosDestino}
      >
        <Transferencia.Alerta tipo="erro">
          {Transferencia.CONSTANTES.ALERTAS.MENSAGEM_ERRO_SEM_FUNDOS_DESTINO}
        </Transferencia.Alerta>
      </Transferencia.Match>

      <Transferencia.Match
        when={erro === Transferencia.EErroTransferencia.CodeDestino}
      >
        <Transferencia.Alerta tipo="erro">
          {Transferencia.CONSTANTES.ALERTAS.MENSAGEM_ERRO_COD_DESTINO}
        </Transferencia.Alerta>
      </Transferencia.Match>

      <Transferencia.Match
        when={erro === Transferencia.EErroTransferencia.ValorMin}
      >
        <Transferencia.Alerta tipo="erro">
          O valor mínimo de transferência deve ser{' '}
          {Transferencia.formatarValorPadraoBrasileiro(
            consultaOrigem?.vlrMinTransferencia ?? '0',
          )}
        </Transferencia.Alerta>
      </Transferencia.Match>

      <Transferencia.Match
        when={erro === Transferencia.EErroTransferencia.ValorRestante}
      >
        <Transferencia.Alerta tipo="erro">
          {Transferencia.CONSTANTES.ALERTAS.MENSAGEM_ERRO_DISTRIBUICAO.DEVE_SER}
          {Transferencia.formatarValorPadraoBrasileiro(
            distribuicaoValores.somaIntencaoDistribuicaoOrigens,
          )}
          {
            Transferencia.CONSTANTES.ALERTAS.MENSAGEM_ERRO_DISTRIBUICAO
              .VOCE_POSSUI
          }
          {Transferencia.formatarValorPadraoBrasileiro(
            distribuicaoValores.distribuicaoRestante ?? 0.0,
          )}
        </Transferencia.Alerta>
      </Transferencia.Match>

      <Transferencia.Match when={!transferenciaRealizada}>
        <Transferencia.Alerta tipo="atencao">
          <strong>
            {Transferencia.CONSTANTES.ALERTAS.MENSAGEM_DESTAQUE_ATENCAO}
          </strong>
          <br />
          {Transferencia.CONSTANTES.ALERTAS.MENSAGEM_ATENCAO}
          <br />
          {Transferencia.CONSTANTES.VALOR_MINIMO_PERMANENCIA(
            consultaOrigem?.response?.retornoCertificado
              ?.vlrMinPermancenciaFundo,
          )}
          <br />
          {Transferencia.CONSTANTES.VALOR_MINIMO_TRANSFERENCIA(
            consultaOrigem?.response?.retornoCertificado?.vlrMinNovoFundo,
          )}
        </Transferencia.Alerta>
      </Transferencia.Match>

      <Transferencia.Match
        when={
          erro === Transferencia.EErroTransferencia.ComprovanteTransferencia
        }
      >
        <Transferencia.Alerta tipo="erro">
          {
            Transferencia.CONSTANTES.ALERTAS
              .MENSAGEM_ERRO_COMPROVANTE_TRANSFERENCIA
          }
        </Transferencia.Alerta>
      </Transferencia.Match>

      <Transferencia.Match when={transferenciaRealizada}>
        <Transferencia.Alerta tipo="sucesso">
          {Transferencia.CONSTANTES.ALERTAS.MENSAGEM_TRANSFERENCIA_SUCESSO}
        </Transferencia.Alerta>
      </Transferencia.Match>
    </Transferencia.SwitchCase>
  );
};
