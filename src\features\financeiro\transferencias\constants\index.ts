import { PREV_PERMISSIONS } from '@src/corporativo/factories/matrizAcesso/factoryPerfilPermissoes';

export const OpcoesPrimeiroSelect = [
  {
    text: 'Transferência entre fundos',
    value: PREV_PERMISSIONS.TRANSFERENCIA_DE_FUNDOS,
  },
  {
    text: 'Transferência interna com Emissão de Conjugado',
    value: PREV_PERMISSIONS.TRANSFERENCIA_EMISSAO,
  },
  {
    text: 'Transferência entre certificados',
    value: PREV_PERMISSIONS.TRANSFERENCIA_CERTIFICADO,
  },
];

export const FILTRO_TRANSFERENCIA = {
  selectTipoTransferencia: 'Selecione qual serviço deseja:',
};

export const ASSINATURA = {
  AUTENTICACAO: 'Autenticação',
  INVALIDA: 'Não foi possível carregar a assinatura.',
};

export const TIPO_DOCUMENTO = 'Comprovante';

export const CODIGO_REQUISICAO = 'TRANSFUND';
