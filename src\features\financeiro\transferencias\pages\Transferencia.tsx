import * as T from '@src/features/financeiro/transferencias/exports';

export const Transferencia: React.FC = () => {
  const { permissoesMatrizAcesso } = T.useContext(T.MatrizAcessoContext);

  const valuesPrimeiroSelectFiltrado = T.useFiltraTipoTransferencia(
    permissoesMatrizAcesso,
  );

  const { handleSelectChange, value } = T.useInputTipoTransferencia();

  const layoutPorTipo = {
    [T.PREV_PERMISSIONS.TRANSFERENCIA_DE_FUNDOS]: (
      <T.TransferenciaEntreFundosLayout />
    ),
  };

  return (
    <T.Grid>
      <T.GridItem xs="1/2">
        <T.Text variant="text-standard-600" fontColor="content-neutral-05">
          {T.FILTRO_TRANSFERENCIA.selectTipoTransferencia}
        </T.Text>

        <T.Select
          placeholder="Selecione"
          options={valuesPrimeiroSelectFiltrado}
          selectedValues={[value]}
          onChange={handleSelectChange}
          variant="box-classic"
          size="standard"
          sizeWidth="standard"
        />
      </T.GridItem>
      <T.GridItem xs="1">
        <T.TransferenciaProvider>
          <T.TransferenciaServicosProvider>
            {layoutPorTipo[value]}
          </T.TransferenciaServicosProvider>
        </T.TransferenciaProvider>
      </T.GridItem>
    </T.Grid>
  );
};
