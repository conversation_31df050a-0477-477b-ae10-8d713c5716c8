import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const ValidacaoAcoes: React.FC = () => {
  const { consultaDestino, confirmarTransferencia } =
    TransferenciaEntreFundos.useTransferenciaServicosContext();
  const { setTransferenciaRealizada, assinatura } =
    TransferenciaEntreFundos.useTransferenciaContext();
  const { certificadoAtivo } = TransferenciaEntreFundos.useContext(
    TransferenciaEntreFundos.PrevidenciaContext,
  );

  const confirmarValidacaoTransferencia = async () => {
    try {
      await confirmarTransferencia.fetchData({
        numeroCertificado: certificadoAtivo?.certificadoNumero ?? '',
        idTransferencia: consultaDestino.response.numTransferencia,
      });

      setTransferenciaRealizada(true);
    } catch (err) {
      setTransferenciaRealizada(false);
    }
  };

  return (
    <TransferenciaEntreFundos.Grid justify="space-between">
      <TransferenciaEntreFundos.GridItem xs="1/2">
        <TransferenciaEntreFundos.AssinaturaTransferencias />
      </TransferenciaEntreFundos.GridItem>
      <TransferenciaEntreFundos.GridItem xs="1/9">
        <TransferenciaEntreFundos.Button
          variant="secondary"
          onClick={confirmarValidacaoTransferencia}
          disabled={!!assinatura}
        >
          {TransferenciaEntreFundos.CONSTANTES.SESSOES.FOOTER_BOTAO_PROSSEGUIR}
        </TransferenciaEntreFundos.Button>
      </TransferenciaEntreFundos.GridItem>
    </TransferenciaEntreFundos.Grid>
  );
};
