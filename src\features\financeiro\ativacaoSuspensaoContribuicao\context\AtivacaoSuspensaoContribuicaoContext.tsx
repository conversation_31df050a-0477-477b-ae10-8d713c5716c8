import * as AtivacaoSuspensao from '../exports';
import { IResponseStatusCoberturasContratadas } from '../types/EnumsStatusAtivacaoSuspensao.types';

interface IAtivacaoSuspensaoContribuicaoContext {
  contribuicoes: AtivacaoSuspensao.IContribuicaoItem[];
  loading: boolean;
  toggleContribuicao: (index: number) => void;
}

interface IAtivacaoSuspensaoContribuicaoProviderProps {
  children: AtivacaoSuspensao.ReactNode;
  dadosApi?: IResponseStatusCoberturasContratadas | null;
}

const AtivacaoSuspensaoContribuicaoContext =
  AtivacaoSuspensao.React.createContext<
    IAtivacaoSuspensaoContribuicaoContext | undefined
  >(undefined);

export const AtivacaoSuspensaoContribuicaoProvider: React.FC<
  IAtivacaoSuspensaoContribuicaoProviderProps
> = ({ children, dadosApi }) => {
  const formulario = AtivacaoSuspensao.useFormularioAtivacaoSuspensao({
    dadosApi,
  });

  return (
    <AtivacaoSuspensaoContribuicaoContext.Provider value={formulario}>
      {children}
    </AtivacaoSuspensaoContribuicaoContext.Provider>
  );
};

export const useAtivacaoSuspensaoContribuicaoContext = () => {
  const context = AtivacaoSuspensao.useContext(
    AtivacaoSuspensaoContribuicaoContext,
  );
  if (!context) {
    throw new Error(
      'useAtivacaoSuspensaoContribuicaoContext deve ser usado dentro de AtivacaoSuspensaoContribuicaoProvider',
    );
  }
  return context;
};
