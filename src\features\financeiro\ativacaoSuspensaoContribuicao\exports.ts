// React dependencies
export { default as React, useState, useContext, useEffect } from 'react';
export type { ReactNode } from 'react';

// UI Components from design system
export {
  Grid,
  GridItem,
  Text,
  LoadingSpinner,
  Table,
  Button,
  ConditionalRenderer,
  Alert,
  IconInfoRound,
  IconWarningRound,
  IconCheckRound,
  Switch,
  Checkbox,
} from '@cvp/design-system-caixa';

export type { TableColumn } from '@cvp/design-system-caixa/dist/types/table-types';

export { default as styled } from 'styled-components';

export {
  formatarValorPadraoBrasileiro,
  tryGetValueOrDefault,
  checkIfAllItemsAreTrue,
} from '@cvp/utils';

export { For, Match, SwitchCase } from '@cvp/componentes-posvenda';

export { AtivacaoSuspensaoContribuicaoProvider } from './context/AtivacaoSuspensaoContribuicaoContext';
export { useAtivacaoSuspensaoContribuicaoContext } from './context/AtivacaoSuspensaoContribuicaoContext';

export { SolicitacaoAtivacaoSuspensao } from './views/SolicitacaoAtivacaoSuspensao';
export { TabelaContribuicoes } from './components/TabelaContribuicoes/TabelaContribuicoes';
export { SecaoConfirmacao } from './components/SecaoConfirmacao';
export { DadosCertificado } from './components/DadosCertificado';

export { useFormularioAtivacaoSuspensao } from './hooks/useFormularioAtivacaoSuspensao';
export { useAssinaturaAtivacaoSuspensao } from './hooks/useAssinaturaAtivacaoSuspensao';
export { useRecuperarContribuicoesCertificado } from './hooks/useRecuperarContribuicoesCertificado';

export type { IAtivacaoSuspensaoContribuicaoData } from './types/IAtivacaoSuspensaoContribuicaoData';
export type { IContribuicaoItem } from './types/IContribuicaoItem';
export type {
  IBeneficioContribuicaoCertificado,
  IRecuperarContribuicoesCertificadoPayload,
  IRecuperarContribuicoesCertificadoResponse,
  IUseRecuperarContribuicoesCertificado,
} from './types/IRecuperarContribuicoesCertificado';

export type {
  IPayloadCoberturasContratadas,
  IResponseStatusCoberturasContratadas,
} from './types/EnumsStatusAtivacaoSuspensao.types';

export { ATIVACAO_SUSPENSAO_CONSTANTS } from './constants/ativacaoSuspensaoConstants';

export {
  EnumStatusAtivacaosuspensao,
  EnumTipoBeneficios,
  StatusCoberturas,
  EnumCategoria,
  EnumTipoRenda,
} from './types/EnumsStatusAtivacaoSuspensao.types';

export { mockRecuperarContribuicoesCertificado } from './mocks/mockRecuperarContribuicoesCertificado';

export {
  AtivacaoSuspensaoTable,
  StatusButton,
  ContainerPrincipal,
  TituloSecao,
  ContribuicaoCard,
  TotalContainer,
} from './styles';

export { colunasTabelaContribuicoes } from './factory/colunasTabelaContribuicoes';

export { dadosTabelaCertificadoFactory } from './factory/dadosTabelaCertificadoFactory';
export { dadosTabelaCuidadoExtraFactory } from './factory/dadosTabelaCuidadoExtraFactory';
export { colunasConfirmacaoFactory } from './factory/colunasConfirmacaoFactory';
export type { IDadosCertificado } from './factory/dadosTabelaCertificadoFactory';
export type { IDadosCuidadoExtra } from './factory/dadosTabelaCuidadoExtraFactory';
