export enum EnumStatusAtivacaosuspensao {
  Ativo = 'AC',
  Suspenso = 'SU',
  Beneficio = 'B',
}

export enum EnumTipoBeneficios {
  PREV = 'PR',
  RISCO = 'RI',
}

export enum StatusCoberturas {
  CoberturaSuspensa = 'U',
  CoberturaAtiva = 'A',
}

export enum EnumCategoria {
  Coberturas = 'RE',
}

export enum EnumTipoRenda {
  Mensal = '7',
}

export interface IPayloadCoberturasContratadas {
  Cpf: string;
  NumeroCertificado: string;
  categoria: string;
  cpfCnpj: string;
}

export interface IResponseStatusCoberturasContratadas {
  empresaId: string;
  contaId: string;
  nomePessoaCertificado: string;
  cpfPessoaCertificado: string;
  produtoId: string;
  descricaoProduto: string;
  subCategoriaProduto: string;
  produtoAgredado: string;
  valorTotalSaldo: string;
  beneficioContribuicaoCertificado: IBeneficioContribuicaoCertificado[];
}

export interface IBeneficioContribuicaoCertificado {
  identificadorLegal: string;
  descricaoRenda: string;
  fatorRenda: string;
  indRevisaoPermitida: string | null;
  tipoContribuicao: string;
  nomeContribuicao: string;
  origemContribuicao: string;
  categoriaContribuicao: string;
  periodicidade: string;
  statusContribuicao: string;
  dataPagamento: string;
  diaPagamento: string;
  valorPagamento: string;
  dataProximoPagamento: string;
  planoId: string;
  beneficioId: string;
  descricaoBeneficio: string;
  tipoBeneficio: string;
  statusBeneficio: string;
  subStatusBeneficio: string;
  valorBeneficio: string;
  valorContribuicaoEsperado: string;
  termoDesejado: string | null;
  tipoRenda: string;
  valorBeneficioEsperado: string;
  tipoCobertura: string;
  tipoCalculo: string;
  status: string | null;
}
