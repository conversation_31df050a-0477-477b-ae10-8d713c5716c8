import React from 'react';
import ReactD<PERSON><PERSON>lient from 'react-dom/client';
import singleSpaReact from 'single-spa-react';
import App from './App';

// Declaração de tipos para HMR
declare global {
  interface NodeModule {
    hot?: {
      accept(path?: string, callback?: () => void): void;
    };
  }
}

const lifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: App,
  errorBoundary(err, info) {
    return (
      <>
        <h1>Oop! 500</h1>
        <pre>{err.stack}</pre>
        <pre>{info.componentStack}</pre>
      </>
    );
  },
});

// Enable Hot Module Replacement
if ((module as any).hot) {
  (module as any).hot.accept('./App', () => {
    // Re-render the app when App component changes
    console.log('🔥 Hot reloading App component');
  });
}

export const { bootstrap, mount, unmount } = lifecycles;
