# Hot Module Replacement (HMR) - Configuração

Este documento descreve como o HMR foi configurado no projeto para melhorar a experiência de desenvolvimento.

## O que é HMR?

Hot Module Replacement (HMR) permite que você atualize módulos em tempo real durante o desenvolvimento sem perder o estado da aplicação ou fazer um reload completo da página.

## Benefícios

- ✅ **Preservação de Estado**: Mantém o estado local dos componentes React
- ✅ **Desenvolvimento Mais Rápido**: Atualizações instantâneas sem reload completo
- ✅ **Melhor Experiência**: Não perde dados de formulários ou posição de scroll
- ✅ **Feedback Imediato**: Vê as mudanças imediatamente após salvar

## Configuração Implementada

### 1. Dependências Instaladas

```bash
npm install --save-dev @pmmmwh/react-refresh-webpack-plugin react-refresh
```

### 2. Webpack Configuration

O `webpack.config.js` foi atualizado com:

- **React Refresh Plugin**: Para preservar estado dos componentes React
- **Dev Server HMR**: Configuração otimizada do servidor de desenvolvimento
- **Source Maps**: Para melhor debugging em desenvolvimento

### 3. Babel Configuration

O `babel.config.js` já estava configurado com o plugin `react-refresh/babel` para desenvolvimento.

### 4. Entrada da Aplicação

Os arquivos principais foram atualizados para aceitar HMR:
- `src/CVP-PlataformaCaixa-PosVenda-Previdencia.tsx`
- `src/App.tsx`

## Como Usar

### Iniciar o Servidor de Desenvolvimento

```bash
npm start
```

O servidor iniciará em `http://localhost:4002` com HMR ativado.

### Testando o HMR

1. Abra a aplicação no navegador
2. Faça uma alteração em qualquer componente React
3. Salve o arquivo
4. Observe que a mudança aparece instantaneamente sem reload da página

### Indicadores de HMR Ativo

No console do webpack-dev-server, você verá:
- Mensagens de hot-update sendo geradas
- Compilações rápidas (poucos segundos)
- Arquivos `.hot-update.js` sendo criados

## Configurações Avançadas

### Desabilitar HMR (se necessário)

Para desabilitar temporariamente o HMR, você pode:

1. **Via linha de comando**:
   ```bash
   npm start -- --no-hot
   ```

2. **Via variável de ambiente**:
   ```bash
   WEBPACK_SERVE_HOT=false npm start
   ```

### Configurações do Dev Server

As seguintes configurações estão ativas:

```javascript
devServer: {
  hot: true,                    // HMR ativado
  liveReload: false,           // Desabilita live reload para usar apenas HMR
  client: {
    overlay: {
      errors: true,            // Mostra erros na tela
      warnings: false,         // Não mostra warnings na tela
    },
  },
}
```

## Troubleshooting

### HMR não está funcionando

1. **Verifique se o servidor está rodando corretamente**:
   - Deve mostrar mensagens sobre HMR no console
   - URL deve ser acessível

2. **Verifique o console do navegador**:
   - Deve mostrar conexão WebSocket ativa
   - Mensagens de hot update

3. **Verifique se as dependências estão instaladas**:
   ```bash
   npm list @pmmmwh/react-refresh-webpack-plugin react-refresh
   ```

### Componentes não atualizam

- Certifique-se de que os componentes são exportados como default
- Evite arrow functions anônimas como componentes principais
- Use React.memo() para componentes que não atualizam

### Estado sendo perdido

- Verifique se o React Fast Refresh está funcionando
- Alguns tipos de mudanças (como mudanças em hooks) podem causar remount
- Estado em Context Providers pode ser preservado

## Comandos Úteis

```bash
# Iniciar com HMR (padrão)
npm start

# Iniciar em modo homologação com HMR
npm run start:hm

# Iniciar em modo standalone com HMR
npm run start:standalone

# Build para desenvolvimento (sem HMR)
npm run build:dev
```

## Notas Importantes

- HMR só funciona em modo de desenvolvimento
- Algumas mudanças estruturais podem requerer reload manual
- O HMR funciona melhor com componentes funcionais React
- Estado global (Redux, Context) é preservado durante hot updates
