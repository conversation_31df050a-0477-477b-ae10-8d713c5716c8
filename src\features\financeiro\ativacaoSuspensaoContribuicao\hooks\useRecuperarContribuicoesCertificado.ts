import { useContext } from 'react';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault, getSessionItem } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import {
  IPayloadCoberturasContratadas,
  IResponseStatusCoberturasContratadas,
  EnumCategoria,
} from '../types/EnumsStatusAtivacaoSuspensao.types';

const useObterCoberturasContratadas = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const payload: IPayloadCoberturasContratadas = {
    Cpf: cpfCnpj,
    NumeroCertificado: certificadoAtivo?.certificadoNumero || '',
    categoria: EnumCategoria.Coberturas,
    cpfCnpj,
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      IPayloadCoberturasContratadas,
      IResponseStatusCoberturasContratadas
    >(PECOS.RecuperarContribuicoesCertificado, {
      data: payload,
      autoFetch: true,
      cache: true,
      cacheKey: `${cpfCnpj}-${certificadoAtivo?.certificadoNumero}`,
    });

  return {
    loading,
    response: tryGetValueOrDefault([response?.entidade], null),
    invocarApiGatewayCvpComToken,
  };
};

export { useObterCoberturasContratadas as useRecuperarContribuicoesCertificado };
